fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
alembic==1.12.1
pydantic[email]==2.4.2
pydantic-settings==2.0.3
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
aiofiles==23.2.1
httpx==0.25.2
aiohttp==3.9.1
redis==5.0.1
celery==5.3.4
opencv-python>=4.8.0
pillow==10.0.1
moviepy==1.0.3
pydub==0.25.1
openai==1.3.6
websockets==12.0
python-dotenv==1.0.0
mutagen==1.47.0
loguru==0.7.2
aiosqlite==0.19.0
pyyaml==6.0.1
numpy<2.0.0
openai-whisper==20231117
playwright>=1.40.0
# Note: openai-whisper requires 'torch'. 
# If not already installed, it will be installed automatically (usually the CPU version).
# For GPU support, please install the appropriate version of torch for your CUDA environment.

# Development dependencies
black==23.9.1
flake8==6.1.0
mypy==1.6.1
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
ffmpeg-python==0.2.0
pandas==2.1.3
openpyxl==3.1.2

# Build and deployment dependencies
pyinstaller==6.3.0
