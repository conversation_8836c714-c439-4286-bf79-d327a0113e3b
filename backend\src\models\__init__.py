"""
基础数据模型
"""

from sqlalchemy import Column, String, DateTime, Boolean, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
import uuid

Base = declarative_base()

class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_deleted = Column(Boolean, default=False)
    
    def to_dict(self):
        """转换为字典"""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if hasattr(value, 'isoformat'):
                # 处理datetime对象
                result[column.name] = value.isoformat() + 'Z' if value else None
            else:
                result[column.name] = value
        return result

# 导入所有模型
from .settings import Settings
from .resources import BackgroundMusic, VideoMaterial, Prompt, CoverTemplate
from .accounts import Account
from .video_generation import VideoGenerationJob, VideoGenerationTask, TaskLog, TaskStatus

__all__ = [
    "BaseModel",
    "Settings",
    "BackgroundMusic",
    "VideoMaterial", 
    "Prompt",
    "Account",
    "CoverTemplate",
    "VideoGenerationJob",
    "VideoGenerationTask", 
    "TaskLog",
    "TaskStatus"
]
