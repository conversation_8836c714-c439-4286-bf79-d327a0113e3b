'use client'

import React, { useState, useEffect } from 'react'
import {
  QueueListIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  TrashIcon,
  EyeIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  XMarkIcon,
  DocumentTextIcon,
  VideoCameraIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'
import apiService, { 
  VideoGenerationJob, 
  VideoGenerationTask, 
  JobProgress,
  TaskLog 
} from '../../services/apiService'

export default function TasksPage() {
  // Data states
  const [jobs, setJobs] = useState<VideoGenerationJob[]>([])
  const [tasks, setTasks] = useState<VideoGenerationTask[]>([])
  const [selectedJob, setSelectedJob] = useState<string | null>(null)
  
  // UI states
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'>('all')
  const [showLogModal, setShowLogModal] = useState(false)
  const [selectedTaskLogs, setSelectedTaskLogs] = useState<TaskLog[]>([])
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null)
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalJobs, setTotalJobs] = useState(0)
  const itemsPerPage = 10

  // Load data
  useEffect(() => {
    loadJobs()
    // Set up auto-refresh for running jobs
    const interval = setInterval(() => {
      const safeJobs = Array.isArray(jobs) ? jobs : []
      if (safeJobs.some(job => job.status === 'running')) {
        loadJobs()
      }
    }, 5000) // Refresh every 5 seconds if there are running jobs

    return () => clearInterval(interval)
  }, [])

  // Load tasks when a job is selected
  useEffect(() => {
    if (selectedJob) {
      loadTasks(selectedJob)
    }
  }, [selectedJob])

  const loadJobs = async () => {
    setLoading(true)
    try {
      const response = await apiService.videoGeneration.getJobs(currentPage, itemsPerPage)
      if (response.data) {
        setJobs(response.data.jobs)
        setTotalJobs(response.data.total)
      }
    } catch (error) {
      console.error('Failed to load jobs:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadTasks = async (jobId: string) => {
    try {
      const response = await apiService.videoGenerationTasks.getTasks(jobId)
      if (response.data) {
        setTasks(response.data)
      }
    } catch (error) {
      console.error('Failed to load tasks:', error)
    }
  }

  const loadTaskLogs = async (taskId: string) => {
    try {
      const response = await apiService.videoGenerationTasks.getTaskLogs(taskId)
      if (response.data) {
        setSelectedTaskLogs(response.data)
        setSelectedTaskId(taskId)
        setShowLogModal(true)
      }
    } catch (error) {
      console.error('Failed to load task logs:', error)
    }
  }

  // 防御性检查：确保关键数据都是数组类型
  const safeJobs = Array.isArray(jobs) ? jobs : []
  const safeTasks = Array.isArray(tasks) ? tasks : []
  const safeSelectedTaskLogs = Array.isArray(selectedTaskLogs) ? selectedTaskLogs : []

  // Filter jobs
  const filteredJobs = safeJobs.filter(job => {
    const matchesSearch = job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (job.description && job.description.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter
    return matchesSearch && matchesStatus
  })

  // Filter tasks
  const filteredTasks = safeTasks.filter(task => {
    if (statusFilter === 'all') return true
    return task.status === statusFilter
  })

  // Job control actions
  const handleStartJob = async (jobId: string) => {
    try {
      await apiService.videoGeneration.controlJob(jobId, "start")
      loadJobs()
    } catch (error) {
      alert('启动作业失败')
    }
  }

  const handlePauseJob = async (jobId: string) => {
    try {
      await apiService.videoGeneration.controlJob(jobId, "pause")
      loadJobs()
    } catch (error) {
      alert('暂停作业失败')
    }
  }

  const handleResumeJob = async (jobId: string) => {
    try {
      await apiService.videoGeneration.controlJob(jobId, "resume")
      loadJobs()
    } catch (error) {
      alert('恢复作业失败')
    }
  }

  const handleCancelJob = async (jobId: string) => {
    if (confirm('确定要取消这个作业吗？')) {
      try {
        await apiService.videoGeneration.controlJob(jobId, "cancel")
        loadJobs()
      } catch (error) {
        alert('取消作业失败')
      }
    }
  }

  const handleDeleteJob = async (jobId: string) => {
    if (confirm('确定要删除这个作业吗？此操作无法撤销。')) {
      try {
        await apiService.videoGeneration.deleteJob(jobId)
        loadJobs()
        if (selectedJob === jobId) {
          setSelectedJob(null)
          setTasks([])
        }
      } catch (error) {
        alert('删除作业失败')
      }
    }
  }

  // Task control actions
  const handleRetryTask = async (taskId: string) => {
    try {
      await apiService.videoGenerationTasks.retryTask(taskId)
      if (selectedJob) {
        loadTasks(selectedJob)
      }
    } catch (error) {
      alert('重试任务失败')
    }
  }

  const handleCancelTask = async (taskId: string) => {
    if (confirm('确定要取消这个任务吗？')) {
      try {
        await apiService.videoGenerationTasks.cancelTask(taskId)
        if (selectedJob) {
          loadTasks(selectedJob)
        }
      } catch (error) {
        alert('取消任务失败')
      }
    }
  }

  // Get status icon and color
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />
      case 'running':
        return <ArrowPathIcon className="h-5 w-5 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />
      case 'cancelled':
        return <StopIcon className="h-5 w-5 text-gray-500" />
      case 'paused':
        return <PauseIcon className="h-5 w-5 text-orange-500" />
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    const statusMap = {
      'pending': '等待中',
      'running': '运行中',
      'completed': '已完成',
      'failed': '失败',
      'cancelled': '已取消',
      'paused': '已暂停'
    }
    return statusMap[status as keyof typeof statusMap] || status
  }

  // Get progress percentage
  const getProgress = (job: VideoGenerationJob) => {
    if (job.total_tasks === 0) return 0
    return Math.round((job.completed_tasks / job.total_tasks) * 100)
  }

  const getTaskProgress = (task: VideoGenerationTask) => {
    return task.progress || 0
  }

  // Statistics
  const stats = {
    total: safeJobs.length,
    pending: safeJobs.filter(j => j.status === 'pending').length,
    running: safeJobs.filter(j => j.status === 'running').length,
    completed: safeJobs.filter(j => j.status === 'completed').length,
    failed: safeJobs.filter(j => j.status === 'failed').length
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ArrowPathIcon className="mx-auto h-12 w-12 text-blue-500 animate-spin" />
          <p className="mt-4 text-lg text-gray-600">加载任务数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">任务管理</h1>
          <p className="text-gray-600">监控和管理视频生成作业和任务</p>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <QueueListIcon className="h-8 w-8 text-gray-500" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{stats.total}</p>
                <p className="text-sm text-gray-600">总作业数</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <ClockIcon className="h-8 w-8 text-yellow-500" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{stats.pending}</p>
                <p className="text-sm text-gray-600">等待中</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <ArrowPathIcon className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{stats.running}</p>
                <p className="text-sm text-gray-600">运行中</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <CheckCircleIcon className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{stats.completed}</p>
                <p className="text-sm text-gray-600">已完成</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="flex items-center">
              <XCircleIcon className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{stats.failed}</p>
                <p className="text-sm text-gray-600">失败</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Jobs List */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">视频生成作业</h2>
                <button
                  onClick={loadJobs}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                >
                  <ArrowPathIcon className="h-5 w-5" />
                </button>
              </div>

              {/* Search and Filter */}
              <div className="flex space-x-4 mb-4">
                <div className="flex-1 relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="搜索作业..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">全部状态</option>
                  <option value="pending">等待中</option>
                  <option value="running">运行中</option>
                  <option value="paused">已暂停</option>
                  <option value="completed">已完成</option>
                  <option value="failed">失败</option>
                  <option value="cancelled">已取消</option>
                </select>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {filteredJobs.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <QueueListIcon className="mx-auto h-12 w-12 text-gray-300" />
                  <p className="mt-4">没有找到匹配的作业</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredJobs.map(job => (
                    <div
                      key={job.id}
                      className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                        selectedJob === job.id ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                      }`}
                      onClick={() => setSelectedJob(job.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(job.status)}
                            <h3 className="font-medium text-gray-900">{job.name}</h3>
                          </div>
                          
                          <p className="text-sm text-gray-600 mt-1">
                            {job.description || '无描述'}
                          </p>
                          
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span>{getStatusText(job.status)}</span>
                            <span>{job.completed_tasks}/{job.total_tasks} 任务</span>
                            <span>{new Date(job.created_at).toLocaleDateString()}</span>
                          </div>
                          
                          {/* Progress bar */}
                          <div className="mt-2">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${getProgress(job)}%` }}
                              />
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              进度: {getProgress(job)}%
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-1 ml-4">
                          {job.status === 'pending' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleStartJob(job.id)
                              }}
                              className="p-1 text-green-600 hover:bg-green-50 rounded"
                              title="启动作业"
                            >
                              <PlayIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          {job.status === 'running' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handlePauseJob(job.id)
                              }}
                              className="p-1 text-orange-600 hover:bg-orange-50 rounded"
                              title="暂停作业"
                            >
                              <PauseIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          {job.status === 'paused' && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleResumeJob(job.id)
                              }}
                              className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                              title="恢复作业"
                            >
                              <PlayIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          {(job.status === 'running' || job.status === 'paused') && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleCancelJob(job.id)
                              }}
                              className="p-1 text-red-600 hover:bg-red-50 rounded"
                              title="取消作业"
                            >
                              <StopIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          {(job.status === 'completed' || job.status === 'failed' || job.status === 'cancelled') && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleDeleteJob(job.id)
                              }}
                              className="p-1 text-red-600 hover:bg-red-50 rounded"
                              title="删除作业"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Tasks List */}
          <div className="bg-white rounded-lg shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {selectedJob ? '任务详情' : '选择作业查看任务'}
              </h2>
              {selectedJob && (
                <p className="text-sm text-gray-600 mt-1">
                  {safeJobs.find(j => j.id === selectedJob)?.name}
                </p>
              )}
            </div>

            <div className="max-h-96 overflow-y-auto">
              {!selectedJob ? (
                <div className="p-8 text-center text-gray-500">
                  <VideoCameraIcon className="mx-auto h-12 w-12 text-gray-300" />
                  <p className="mt-4">请选择一个作业查看具体任务</p>
                </div>
              ) : filteredTasks.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <QueueListIcon className="mx-auto h-12 w-12 text-gray-300" />
                  <p className="mt-4">该作业暂无任务</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredTasks.map(task => (
                    <div key={task.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(task.status)}
                            <h4 className="font-medium text-gray-900">{task.task_name}</h4>
                          </div>
                          
                          <div className="mt-1 text-sm text-gray-600">
                            {task.current_step && (
                              <p>当前步骤: {task.current_step}</p>
                            )}
                            {task.error_message && (
                              <p className="text-red-600">错误: {task.error_message}</p>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            <span>{getStatusText(task.status)}</span>
                            {task.retry_count && task.retry_count > 0 && (
                              <span>重试次数: {task.retry_count}</span>
                            )}
                            <span>{new Date(task.created_at).toLocaleDateString()}</span>
                          </div>
                          
                          {/* Task progress bar */}
                          {task.status === 'running' && (
                            <div className="mt-2">
                              <div className="w-full bg-gray-200 rounded-full h-1.5">
                                <div
                                  className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                                  style={{ width: `${getTaskProgress(task)}%` }}
                                />
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                {getTaskProgress(task)}%
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-1 ml-4">
                          {task.status === 'failed' && (
                            <button
                              onClick={() => handleRetryTask(task.id)}
                              className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                              title="重试任务"
                            >
                              <ArrowPathIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          {(task.status === 'running' || task.status === 'pending') && (
                            <button
                              onClick={() => handleCancelTask(task.id)}
                              className="p-1 text-red-600 hover:bg-red-50 rounded"
                              title="取消任务"
                            >
                              <StopIcon className="h-4 w-4" />
                            </button>
                          )}
                          
                          <button
                            onClick={() => loadTaskLogs(task.id)}
                            className="p-1 text-gray-600 hover:bg-gray-50 rounded"
                            title="查看日志"
                          >
                            <DocumentTextIcon className="h-4 w-4" />
                          </button>
                          
                          {task.final_video_path && (
                            <button
                              onClick={() => {
                                // TODO: Implement video preview/download
                                alert('视频预览功能开发中')
                              }}
                              className="p-1 text-green-600 hover:bg-green-50 rounded"
                              title="查看结果"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Task Logs Modal */}
      {showLogModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-96">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  任务日志 - {selectedTaskId}
                </h3>
                <button
                  onClick={() => setShowLogModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>
            
            <div className="max-h-80 overflow-y-auto p-6">
              {safeSelectedTaskLogs.length === 0 ? (
                <p className="text-gray-500 text-center">暂无日志</p>
              ) : (
                <div className="space-y-2">
                  {safeSelectedTaskLogs.map(log => (
                    <div
                      key={log.id}
                      className={`p-2 rounded text-sm ${
                        log.level === 'error' ? 'bg-red-50 text-red-700' :
                        log.level === 'warning' ? 'bg-yellow-50 text-yellow-700' :
                        'bg-gray-50 text-gray-700'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <span className="flex-1">{log.message}</span>
                        <span className="text-xs text-gray-500 ml-2">
                          {new Date(log.created_at).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
