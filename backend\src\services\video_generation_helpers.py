"""
视频生成服务的辅助方法
"""

import os
import random
import uuid
import re
import asyncio
import subprocess
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger
import ffmpeg
import traceback
from sqlalchemy.orm import Session, sessionmaker

from .llm_service import LLMService
from .tts_service import TTSService

from ..models import VideoMaterial, BackgroundMusic, Prompt, Account, CoverTemplate, VideoGenerationJob, VideoGenerationTask
from ..schemas.video_generation import AudioAnalysis


# 定义一个统一的根目录，所有文件操作都基于此
# __file__ 指向当前文件 (video_generation_helpers.py)
# .parent.parent -> src/
# .parent.parent.parent -> backend/
BACKEND_DIR = Path(__file__).parent.parent.parent
UPLOADS_DIR = BACKEND_DIR / "uploads"


async def run_ffmpeg_async(ffmpeg_stream, description="FFmpeg操作"):
    """异步执行FFmpeg命令，避免阻塞事件循环"""
    try:
        # 获取FFmpeg命令参数
        cmd = ffmpeg_stream.compile()
        logger.info(f"开始异步执行{description}: {' '.join(cmd[:3])}...")
        
        # 定义同步执行函数，然后用asyncio.to_thread包裹
        def run_ffmpeg_sync():
            import subprocess
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False  # 保持二进制输出
            )
            return result
        
        # 在线程池中异步执行FFmpeg，避免阻塞事件循环
        logger.info(f"在线程池中执行{description}...")
        result = await asyncio.to_thread(run_ffmpeg_sync)
        
        if result.returncode != 0:
            error_msg = result.stderr.decode('utf-8', errors='ignore') if result.stderr else "未知错误"
            logger.error(f"{description}失败 (返回码: {result.returncode}): {error_msg}")
            raise ffmpeg.Error('ffmpeg', result.stdout, result.stderr)
        
        logger.info(f"{description}完成")
        return True
        
    except Exception as e:
        logger.error(f"{description}异步执行失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise


class VideoCompositionService:
    """视频合成服务，使用 ffmpeg-python"""

    @staticmethod
    async def _create_intermediate_video(
        material_paths: List[str],
        material_durations: List[float],
        target_duration: float,
        width: int, height: int, fps: int,
        output_path: str
    ) -> bool:
        """第一步：创建标准化的中间视频"""
        import ffmpeg
        try:
            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"确保中间视频输出目录存在: {output_dir}")
            total_duration = sum(material_durations)
            if total_duration == 0:
                raise ValueError("所有视频素材均无效或时长为0")
            
            loops_needed = 1
            if total_duration < target_duration:
                loops_needed = int(target_duration // total_duration) + 1

            base_streams = []
            for path in material_paths:
                stream = ffmpeg.input(filename=path).video
                processed_stream = (
                    stream
                    .filter('scale', width, height, force_original_aspect_ratio='decrease')
                    .filter('pad', width, height, x='(ow-iw)/2', y='(oh-ih)/2')
                    .filter('setsar', 1)
                )
                base_streams.append(processed_stream)

            final_streams_to_concat = []
            if loops_needed > 1:
                split_nodes = [s.filter('split', loops_needed).node for s in base_streams]
                for i in range(loops_needed):
                    for node in split_nodes:
                        final_streams_to_concat.append(node[i])
            else:
                final_streams_to_concat = base_streams
            
            concatenated_stream = ffmpeg.concat(*final_streams_to_concat, v=1, a=0)
            
            final_stream = (
                concatenated_stream
                .filter('fps', fps)
                .trim(duration=target_duration)
            )

            # 异步执行FFmpeg
            ffmpeg_stream = (
                ffmpeg
                .output(final_stream, output_path, vcodec='libx264', preset='ultrafast', pix_fmt='yuv420p')
                .overwrite_output()
            )
            await run_ffmpeg_async(ffmpeg_stream, "创建中间视频")
            
            logger.info(f"成功创建中间视频: {output_path}")
            return True
        except ffmpeg.Error as e:
            logger.error(f"创建中间视频失败: {e.stderr.decode()}")
            return False

    @staticmethod
    async def compose_video(
        task,
        materials: List[VideoMaterial],
        background_music: BackgroundMusic,
        audio_duration: float,
        cover_image_path: str,
        first_sentence_duration: float,
        subtitle_file_path: str,
        output_path: str,
        video_settings: Dict[str, Any],
        audio_settings: Optional[Dict[str, Any]] = None,
        subtitle_settings: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        使用两步合成法合成最终视频
        """
        import ffmpeg
        
        temp_dir = UPLOADS_DIR / "temp"
        temp_dir.mkdir(parents=True, exist_ok=True)
        intermediate_video_path = temp_dir / f"intermediate_{task.id}.mp4"

        try:
            # --- 路径处理 ---
            material_files = [(BACKEND_DIR / m.file_path).resolve().as_posix() for m in materials]
            music_file = (BACKEND_DIR / background_music.file_path).resolve().as_posix()
            speech_file = (BACKEND_DIR / task.audio_file_path).resolve().as_posix()
            cover_file = (BACKEND_DIR / cover_image_path).resolve().as_posix()
            subtitle_file = (BACKEND_DIR / subtitle_file_path).resolve().as_posix()
            output_file = str((BACKEND_DIR / output_path).resolve())

            # 确保输出目录存在
            output_dir = Path(output_file).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"确保输出目录存在: {output_dir}")

            resolution = video_settings.get('resolution', '1080x1920')
            fps = video_settings.get('fps', 30)
            width, height = map(int, resolution.split('x'))

            # --- 第一步: 创建中间视频 ---
            # 获取材料的实际持续时间值
            material_durations = []
            for m in materials:
                if hasattr(m, 'duration') and m.duration is not None:
                    # 如果是数据库对象，获取实际值
                    duration_value = float(m.duration) if isinstance(m.duration, (int, float, str)) else 0.0
                    material_durations.append(duration_value)
                else:
                    material_durations.append(0.0)
            
            success = await VideoCompositionService._create_intermediate_video(
                material_files,
                material_durations,
                audio_duration,
                width, height, fps,
                str(intermediate_video_path)
            )
            if not success:
                raise RuntimeError("创建中间视频失败")

            # --- 第二步: 最终合成 ---
            video_stream = ffmpeg.input(filename=str(intermediate_video_path))
            
            # 获取音频设置，设置默认值
            if audio_settings is None:
                audio_settings = {
                    'speech_volume': 1.0,
                    'background_music_volume': 0.15,
                    'enable_background_music': True
                }
            
            # 获取字幕设置，设置默认值
            if subtitle_settings is None:
                subtitle_settings = {
                    'font_family': 'Arial',
                    'font_size': 24,
                    'font_color': '#FFFFFF',
                    'position': 'bottom',
                    'enabled': True
                }
            
            audio_stream = VideoCompositionService._prepare_combined_audio(
                speech_file, music_file, audio_duration,
                speech_volume=audio_settings.get('speech_volume', 1.0),
                music_volume=audio_settings.get('background_music_volume', 0.15),
                enable_background_music=audio_settings.get('enable_background_music', True)
            )
            
            # 等比例缩放封面，保持纵横比
            # 设置封面宽度为视频宽度的80%，高度自动等比例计算
            cover_width = int(width * 0.8)  # 80% of video width
            
            # 创建基础缩放后的封面
            cover_scaled = (
                ffmpeg
                .input(filename=cover_file)
                .filter('scale', cover_width, -1)  # -1 表示高度自动计算以保持纵横比
            )
            
            # 添加圆角遮罩效果
            # 计算圆角半径，约为封面宽度的6%（更明显的圆角效果）
            corner_radius = int(cover_width * 0.06)
            logger.info(f"为封面添加圆角效果，半径: {corner_radius}px")
            
            try:
                # 创建带圆角的封面图像文件
                rounded_cover_path = temp_dir / f"rounded_cover_{task.id}.png"
                success = VideoCompositionService._create_rounded_cover_image(
                    cover_file, cover_width, corner_radius, str(rounded_cover_path)
                )
                
                if success and rounded_cover_path.exists():
                    # 使用创建好的圆角封面图像
                    cover_overlay = ffmpeg.input(filename=str(rounded_cover_path))
                    logger.info("✅ 圆角封面图像创建成功")
                else:
                    raise Exception("圆角封面创建失败")
                    
            except Exception as e:
                logger.warning(f"圆角封面创建失败，回退到方角封面: {e}")
                # 如果圆角处理失败，回退到原来的方角封面
                cover_overlay = cover_scaled
            
            # 封面显示时长验证和调整
            logger.info(f"封面显示时长设置: {first_sentence_duration}秒")
            
            # 验证时长合理性（避免过短或过长）
            min_cover_duration = 1.0  # 最短1秒
            max_cover_duration = 10.0  # 最长10秒
            
            if first_sentence_duration < min_cover_duration:
                adjusted_duration = min_cover_duration
                logger.warning(f"第一句话时长过短({first_sentence_duration}s)，调整为最小值{adjusted_duration}s")
            elif first_sentence_duration > max_cover_duration:
                adjusted_duration = max_cover_duration
                logger.warning(f"第一句话时长过长({first_sentence_duration}s)，调整为最大值{adjusted_duration}s")
            else:
                adjusted_duration = first_sentence_duration
                logger.info(f"封面显示时长验证通过: {adjusted_duration}s")
            
            video_with_cover = video_stream.overlay(
                cover_overlay,
                x='(main_w-overlay_w)/2',
                y='(main_h-overlay_h)/2',
                enable=f'between(t,0,{adjusted_duration})'
            )

            # 配置字幕样式和位置
            font_name = subtitle_settings.get('font_family', 'Arial')
            font_size = subtitle_settings.get('font_size', 24)
            font_color = subtitle_settings.get('font_color', '#FFFFFF')
            position = subtitle_settings.get('position', 'bottom')
            subtitle_enabled = subtitle_settings.get('enabled', True)
            
            logger.info(f"字幕配置 - 字体: {font_name}, 大小: {font_size}, 颜色: {font_color}, 位置: {position}, 启用: {subtitle_enabled}")
            
            # 如果字幕被禁用，跳过字幕处理
            if not subtitle_enabled:
                final_video = video_with_cover
                logger.info("字幕已禁用，跳过字幕处理")
            else:
                # 应用字幕样式配置
                logger.info(f"应用字幕配置 - 字体: {font_name}, 大小: {font_size}, 颜色: {font_color}, 位置: {position}")
                
                try:
                    # 获取描边配置
                    stroke_thickness = subtitle_settings.get('stroke_thickness', 2)
                    stroke_color = subtitle_settings.get('stroke_color', '#000000')

                    # 检查是否使用默认样式（如果所有样式都是默认值，使用简单模式）
                    is_default_style = (
                        font_name == 'Arial' and
                        font_size == 24 and
                        font_color == '#FFFFFF' and
                        position == 'bottom' and
                        stroke_thickness <= 2 and
                        stroke_color == '#000000'
                    )
                    
                    if is_default_style:
                        # 使用基础SRT字幕（最稳定）
                        logger.info("使用默认样式，应用基础SRT字幕")
                        final_video = video_with_cover.filter('subtitles', filename=subtitle_file)
                    else:
                        # 使用ASS格式字幕支持自定义样式
                        logger.info("使用自定义样式，生成ASS字幕")
                        
                        # 生成ASS字幕文件
                        ass_file = subtitle_file.replace('.srt', '.ass')

                        VideoCompositionService._create_ass_subtitle_file(
                            subtitle_file, ass_file, font_name, font_size, font_color, position,
                            stroke_thickness, stroke_color
                        )
                        
                        # 应用ASS字幕
                        final_video = video_with_cover.filter('subtitles', filename=ass_file)
                        logger.info(f"✅ ASS字幕应用成功: {ass_file}")
                    
                    logger.info("✅ 字幕样式应用成功")
                    
                except Exception as e:
                    logger.error(f"❌ 字幕处理失败: {e}")
                    # 回退到基础字幕模式
                    try:
                        logger.warning("回退到基础SRT字幕模式")
                        final_video = video_with_cover.filter('subtitles', filename=subtitle_file)
                        logger.info("✅ 基础字幕回退成功")
                    except Exception as e2:
                        logger.error(f"❌ 基础字幕回退也失败: {e2}")
                        # 如果连基础字幕都失败，不添加字幕
                        final_video = video_with_cover
                        logger.warning("完全回退到无字幕模式")

            # 异步执行最终视频合成
            ffmpeg_stream = (
                ffmpeg
                .output(final_video, audio_stream, output_file, vcodec='libx264', acodec='aac', preset='fast', pix_fmt='yuv420p')
                .overwrite_output()
            )
            await run_ffmpeg_async(ffmpeg_stream, "最终视频合成")
            
            logger.info(f"视频合成完成: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"视频合成失败 (非ffmpeg错误): {e}")
            return False
        finally:
            # 清理临时文件
            if intermediate_video_path.exists():
                os.remove(intermediate_video_path)
            # 清理圆角相关临时文件
            mask_path = temp_dir / f"rounded_mask_{task.id}.png"
            if mask_path.exists():
                os.remove(mask_path)
            rounded_cover_path = temp_dir / f"rounded_cover_{task.id}.png"
            if rounded_cover_path.exists():
                os.remove(rounded_cover_path)
    
    @staticmethod
    def _prepare_combined_audio(speech_path: str, music_path: str, target_duration: float, 
                                speech_volume: float = 1.0, music_volume: float = 0.15, 
                                enable_background_music: bool = True):
        """
        准备合成音频
        :param speech_path: 语音文件路径
        :param music_path: 背景音乐文件路径  
        :param target_duration: 目标时长
        :param speech_volume: 语音音量 (0.0-1.0)
        :param music_volume: 背景音乐音量 (0.0-1.0)
        :param enable_background_music: 是否启用背景音乐
        """
        speech_stream = ffmpeg.input(filename=speech_path).audio
        
        # 调整语音音量
        if speech_volume != 1.0:
            speech_stream = speech_stream.filter('volume', volume=speech_volume)
        
        # 如果禁用背景音乐，只返回语音
        if not enable_background_music:
            return speech_stream
        
        # 添加背景音乐并调整音量
        music_stream = ffmpeg.input(filename=music_path, stream_loop=-1).audio.filter('atrim', duration=target_duration)
        if music_volume != 1.0:
            music_stream = music_stream.filter('volume', volume=music_volume)
        
        # 混合音频流
        return ffmpeg.filter(
            [speech_stream, music_stream], 'amix', inputs=2, duration='first', dropout_transition=3, weights="1 1"
        )

    @staticmethod
    def _create_ass_subtitle_file(srt_path: str, ass_path: str, font_name: str, font_size: int,
                                 font_color: str, position: str, stroke_thickness: int = 2,
                                 stroke_color: str = '#000000'):
        """
        将SRT文件转换为ASS文件，应用自定义样式（包括描边）
        """
        try:
            # 将HTML颜色转换为ASS格式
            def html_color_to_ass(html_color):
                if html_color.startswith('#'):
                    html_color = html_color[1:]
                r = int(html_color[0:2], 16)
                g = int(html_color[2:4], 16) 
                b = int(html_color[4:6], 16)
                return f"&H00{b:02X}{g:02X}{r:02X}"
            
            ass_color = html_color_to_ass(font_color)
            ass_stroke_color = html_color_to_ass(stroke_color)

            # 设置对齐方式
            if position == 'top':
                alignment = 8  # 上方居中
                margin_v = 30  # 上边距
            elif position == 'center':
                alignment = 5  # 中间居中
                margin_v = 0
            else:  # bottom
                alignment = 2  # 下方居中
                margin_v = 30  # 下边距
            
            # 读取SRT文件
            with open(srt_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()
            
            # 创建ASS文件头部
            ass_content = f"""[Script Info]
Title: Generated Subtitle
ScriptType: v4.00+

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{ass_color},&H000000FF,{ass_stroke_color},&H80000000,0,0,0,0,100,100,0,0,1,{stroke_thickness},0,{alignment},10,10,{margin_v},1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
            
            # 解析SRT并转换为ASS格式
            srt_blocks = srt_content.strip().split('\n\n')
            for block in srt_blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    # 跳过序号
                    time_line = lines[1]
                    text_lines = lines[2:]
                    
                    # 转换时间格式 (SRT: 00:00:01,000 --> 00:00:05,000 to ASS: 0:00:01.00,0:00:05.00)
                    if ' --> ' in time_line:
                        start_time, end_time = time_line.split(' --> ')
                        
                        # 转换SRT时间格式到ASS格式
                        def srt_time_to_ass(srt_time):
                            # SRT: 00:00:01,000 -> ASS: 0:00:01.00
                            time_part, ms_part = srt_time.split(',')
                            hours, minutes, seconds = time_part.split(':')
                            # ASS只需要两位毫秒
                            ms = ms_part[:2]  # 取前两位毫秒
                            # 格式化为ASS时间格式：H:MM:SS.CS (CS是百分之一秒)
                            return f"{int(hours)}:{minutes}:{seconds}.{ms}"
                        
                        start_time_ass = srt_time_to_ass(start_time.strip())
                        end_time_ass = srt_time_to_ass(end_time.strip())
                        
                        text = '\\N'.join(text_lines)  # ASS使用\N换行
                        
                        ass_content += f"Dialogue: 0,{start_time_ass},{end_time_ass},Default,,0,0,0,,{text}\n"
            
            # 写入ASS文件
            with open(ass_path, 'w', encoding='utf-8') as f:
                f.write(ass_content)
            
            logger.info(f"成功创建ASS字幕文件: {ass_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建ASS字幕文件失败: {e}")
            return False

    @staticmethod
    def _create_rounded_mask(cover_file_path: str, corner_radius: int, output_path: str) -> bool:
        """
        使用PIL创建圆角遮罩图像，根据实际封面尺寸
        """
        try:
            from PIL import Image, ImageDraw
            
            # 先获取封面图片的实际尺寸
            with Image.open(cover_file_path) as cover_img:
                orig_width, orig_height = cover_img.size
            
            # 计算缩放后的尺寸（80%宽度）
            # 这里需要根据目标视频尺寸计算
            target_width = int(1080 * 0.8)  # 假设视频宽度1080px
            scale_factor = target_width / orig_width
            target_height = int(orig_height * scale_factor)
            
            # 创建遮罩图像（白色背景表示不透明，黑色表示透明）
            mask = Image.new('L', (target_width, target_height), 0)  # 'L'模式是灰度图，0是黑色
            draw = ImageDraw.Draw(mask)
            
            # 绘制圆角矩形遮罩
            # 先填充整个矩形为白色（不透明）
            draw.rectangle([corner_radius, 0, target_width - corner_radius, target_height], fill=255)
            draw.rectangle([0, corner_radius, target_width, target_height - corner_radius], fill=255)
            
            # 绘制四个圆角
            # 左上角
            draw.ellipse([0, 0, corner_radius * 2, corner_radius * 2], fill=255)
            # 右上角
            draw.ellipse([target_width - corner_radius * 2, 0, target_width, corner_radius * 2], fill=255)
            # 左下角
            draw.ellipse([0, target_height - corner_radius * 2, corner_radius * 2, target_height], fill=255)
            # 右下角
            draw.ellipse([target_width - corner_radius * 2, target_height - corner_radius * 2, target_width, target_height], fill=255)
            
            # 保存遮罩文件
            mask.save(output_path)
            logger.info(f"成功创建圆角遮罩文件: {output_path}, 尺寸: {target_width}x{target_height}")
            return True
            
        except Exception as e:
            logger.error(f"创建圆角遮罩失败: {e}")
            return False

    @staticmethod
    def _create_rounded_cover_image(input_path: str, target_width: int, corner_radius: int, output_path: str) -> bool:
        """
        直接创建带圆角的封面图像
        """
        try:
            from PIL import Image, ImageDraw
            
            # 打开原始封面图像
            with Image.open(input_path) as img:
                # 计算目标尺寸（保持纵横比）
                orig_width, orig_height = img.size
                scale_factor = target_width / orig_width
                target_height = int(orig_height * scale_factor)
                
                # 缩放图像
                img_resized = img.resize((target_width, target_height), Image.Resampling.LANCZOS)
                
                # 确保是RGBA模式
                if img_resized.mode != 'RGBA':
                    img_resized = img_resized.convert('RGBA')
                
                # 创建圆角遮罩
                mask = Image.new('L', (target_width, target_height), 0)
                mask_draw = ImageDraw.Draw(mask)
                
                # 绘制圆角矩形遮罩
                # 中央矩形
                mask_draw.rectangle([corner_radius, 0, target_width - corner_radius, target_height], fill=255)
                mask_draw.rectangle([0, corner_radius, target_width, target_height - corner_radius], fill=255)
                
                # 四个圆角
                mask_draw.ellipse([0, 0, corner_radius * 2, corner_radius * 2], fill=255)
                mask_draw.ellipse([target_width - corner_radius * 2, 0, target_width, corner_radius * 2], fill=255)
                mask_draw.ellipse([0, target_height - corner_radius * 2, corner_radius * 2, target_height], fill=255)
                mask_draw.ellipse([target_width - corner_radius * 2, target_height - corner_radius * 2, target_width, target_height], fill=255)
                
                # 应用遮罩到图像
                # 创建透明背景
                rounded_img = Image.new('RGBA', (target_width, target_height), (0, 0, 0, 0))
                rounded_img.paste(img_resized, (0, 0))
                
                # 应用alpha遮罩
                rounded_img.putalpha(mask)
                
                # 保存圆角图像
                rounded_img.save(output_path, 'PNG')
                logger.info(f"成功创建圆角封面图像: {output_path}, 尺寸: {target_width}x{target_height}, 圆角: {corner_radius}px")
                return True
                
        except Exception as e:
            logger.error(f"创建圆角封面图像失败: {e}")
            return False


class VideoGenerationServiceHelpers:
    """视频生成服务��助方法"""
    
    def __init__(self, session_maker: sessionmaker):
        self.session_maker = session_maker
        self.llm_service = LLMService(session_maker)
        self.tts_service = TTSService(session_maker)
    
    async def _select_materials(
        self, task, job_config: Dict[str, Any], audio_duration: Optional[float] = None
    ) -> List[VideoMaterial]:
        """选择视频素材，根据音频时长智能选择足够的素材"""
        db = self.session_maker()
        try:
            material_group = job_config['video_material_group']
            selection_type = job_config['material_selection']
            
            materials = db.query(VideoMaterial).filter(
                VideoMaterial.category == material_group,
                VideoMaterial.is_deleted == False
            ).all()
            
            if not materials:
                raise ValueError(f"分组 {material_group} 中没有可用素材")
            
            if selection_type == 'manual':
                selected_ids = job_config.get('selected_materials', [])
                if not selected_ids:
                    raise ValueError("手动选择模式下必须指定素材")
                
                selected_materials = [m for m in materials if m.id in selected_ids]
                if not selected_materials:
                    raise ValueError("指定的素材不存在或不在该分组中")
                
                return selected_materials
            else:
                # 智能选择逻辑
                if audio_duration is None:
                    # 如果没有音频时长信息，回退到固定数量选择
                    min_materials = min(5, len(materials))
                    selected_materials = random.sample(materials, min_materials)
                    # 对选中的素材进行随机排序
                    random.shuffle(selected_materials)
                    return selected_materials
                
                # 基于音频时长智能选择素材
                return self._smart_select_materials(materials, audio_duration)
        finally:
            db.close()
    
    def _smart_select_materials(self, materials: List[VideoMaterial], audio_duration: float) -> List[VideoMaterial]:
        """
        基于音频时长智能选择素材
        先随机打乱所有素材，然后智能选择足够的素材，确保每次选择的素材组合都不同
        """
        # 获取所有素材的实际时长
        material_durations = []
        for m in materials:
            if hasattr(m, 'duration') and m.duration is not None:
                duration_value = float(m.duration) if isinstance(m.duration, (int, float, str)) else 0.0
                material_durations.append((m, duration_value))
            else:
                material_durations.append((m, 0.0))
        
        # 首先随机打乱所有素材，确保每次选择的素材组合都不同
        random.shuffle(material_durations)
        logger.info(f"已随机打乱 {len(material_durations)} 个素材的选择顺序")
        
        selected_materials = []
        total_duration = 0.0
        
        # 逐个添加素材直到达到所需时长
        for material, duration in material_durations:
            selected_materials.append(material)
            total_duration += duration
            
            # 如果总时长已经足够，停止添加
            if total_duration >= audio_duration:
                break
        
        # 如果所有素材时长仍不够，记录警告并返回所有素材
        if total_duration < audio_duration:
            logger.warning(f"所有素材总时长 {total_duration}s 小于音频时长 {audio_duration}s，将重复使用素材")
            if not selected_materials:  # 如果没有有效素材，至少返回一个
                selected_materials = [materials[0]] if materials else []
        
        # 确保至少有一个素材
        if not selected_materials and materials:
            selected_materials = [random.choice(materials)]
        
        # 再次随机排序选中的素材，确定最终播放顺序
        random.shuffle(selected_materials)
        
        logger.info(f"智能选择了 {len(selected_materials)} 个素材，总时长: {total_duration}s，目标时长: {audio_duration}s")
        logger.info(f"最终选择的素材ID: {[m.id for m in selected_materials]}")
        # 避免f-string嵌套问题，分开处理时长信息
        duration_info = []
        for m in selected_materials:
            duration_val = getattr(m, 'duration', '未知')
            duration_info.append(f"{m.id}({duration_val}s)")
        logger.info(f"最终播放顺序: {duration_info}")
        return selected_materials
    
    async def _generate_story(
        self, task, job_config: Dict[str, Any]
    ) -> str:
        """生成故事文案"""
        db = self.session_maker()
        try:
            prompt_id = job_config['prompt_id']
            prompt = db.query(Prompt).filter(Prompt.id == prompt_id).first()
            if not prompt:
                raise ValueError(f"提示词不存在: {prompt_id}")
            
            account = db.query(Account).filter(Account.id == task.account_id).first()
            if not account:
                raise ValueError(f"账号不存在: {task.account_id}")
            
            prompt_text = prompt.content.replace('{account_name}', account.name)
            prompt_text = prompt_text.replace('{account_description}', account.description or '')
            
            story = await self.llm_service.generate_text(prompt_text)
            
            if not story or len(story.strip()) < 10:
                raise ValueError("生成的故事内容太短")
            
            # LLM文案后处理：清理不需要的字符
            story = story.strip()
            story = story.replace("...", ".")  # 替换省略号为句号
            
            # 删除所有星号字符
            story = re.sub(r'\*+', '', story)
            
            # 清理多余的空格
            story = re.sub(r'\s+', ' ', story).strip()
            
            return story
        finally:
            db.close()
    
    async def _generate_audio(
        self, task, job_config: Dict[str, Any], story: str
    ) -> str:
        """生成语音文件"""
        voice_settings = job_config['voice_settings']
        voice = voice_settings['voice']
        speed = voice_settings['speed']
        
        audio_dir = UPLOADS_DIR / "audio"
        audio_dir.mkdir(parents=True, exist_ok=True)
        
        # 使用新的命名规则生成文件名
        filename = self._generate_standard_filename(task, "mp3")
        relative_audio_path = Path("uploads") / "audio" / filename
        absolute_audio_path = BACKEND_DIR / relative_audio_path

        success = await self.tts_service.generate_speech(
            text=story,
            voice=voice,
            speed=speed,
            output_path=str(absolute_audio_path)
        )
        
        if not success or not (absolute_audio_path).exists():
            raise ValueError("语音生成失败")
        
        return str(absolute_audio_path)
    
    async def _generate_subtitles(
        self, task, audio_analysis: AudioAnalysis, subtitle_config: Optional[dict] = None
    ) -> str:
        """生成字幕文件"""
        from .video_generation_service import SubtitleGenerator

        subtitle_dir = UPLOADS_DIR / "subtitles"
        subtitle_dir.mkdir(parents=True, exist_ok=True)

        # 使用新的命名规则
        filename = self._generate_standard_filename(task, "srt")
        relative_subtitle_path = Path("uploads") / "subtitles" / filename

        # 获取每屏单词数配置
        words_per_screen = 1  # 默认值
        if subtitle_config:
            words_per_screen = subtitle_config.get('words_per_screen', 1)

        srt_content = SubtitleGenerator.generate_srt(
            audio_analysis.word_timestamps,
            task.generated_story,
            audio_analysis.first_sentence_duration,
            words_per_screen
        )
        
        with open(BACKEND_DIR / relative_subtitle_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        
        return str(relative_subtitle_path)
    
    async def _generate_cover(
        self, task, job_config: Dict[str, Any]
    ) -> str:
        """生成封面图片"""
        from .cover_screenshot_service import cover_screenshot_service
        
        db = self.session_maker()
        try:
            template_id = job_config['cover_template_id']
            logger.info(f"开始生成封面 - 任务ID: {task.id}, 模板ID: {template_id}")
            
            template = db.query(CoverTemplate).filter(CoverTemplate.id == template_id).first()
            if not template:
                logger.error(f"封面模板不存在: {template_id}")
                raise ValueError(f"封面模板不存在: {template_id}")
            
            account = db.query(Account).filter(Account.id == task.account_id).first()
            if not account:
                logger.error(f"账号不存在: {task.account_id}")
                raise ValueError(f"账号不存在: {task.account_id}")
            
            # 使用新的命名规则
            filename = self._generate_standard_filename(task, "png")
            relative_cover_path = Path("uploads") / "covers" / filename
            absolute_cover_path = BACKEND_DIR / relative_cover_path
            
            # 确保目录存在
            absolute_cover_path.parent.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"封面生成参数:")
            logger.info(f"  - 模板: {template.name} ({template_id})")
            logger.info(f"  - 账号: {account.name}")
            logger.info(f"  - 标题: {task.first_sentence}")
            logger.info(f"  - 输出路径: {absolute_cover_path}")
            
            # 使用网页截图服务生成封面
            success = await cover_screenshot_service.generate_cover_for_video_task(
                task=task,
                template_id=template_id,
                account=account,
                title=task.first_sentence,
                output_path=str(absolute_cover_path),
                db=db
            )
            
            logger.info(f"封面生成结果: {'成功' if success else '失败'}")
            
            if not success:
                logger.error(f"封面截图服务返回失败")
                raise RuntimeError("网页截图生成封面失败")
            
            if not absolute_cover_path.exists():
                logger.error(f"封面文件不存在: {absolute_cover_path}")
                raise RuntimeError(f"封面文件生成后不存在: {absolute_cover_path}")
            
            file_size = absolute_cover_path.stat().st_size
            logger.info(f"封面生成完成: {relative_cover_path} ({file_size} bytes)")
            return str(relative_cover_path)
            
        except Exception as e:
            logger.error(f"生成封面过程中发生错误: {e}")
            logger.error(f"任务ID: {task.id}, 模板ID: {job_config.get('cover_template_id', 'Unknown')}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise
        finally:
            db.close()
    
    async def _select_background_music(
        self, task, job_config: Dict[str, Any]
    ) -> BackgroundMusic:
        """选择背景音乐"""
        db = self.session_maker()
        try:
            music_group = job_config['background_music_group']
            music_list = db.query(BackgroundMusic).filter(
                BackgroundMusic.category == music_group,
                BackgroundMusic.is_deleted == False
            ).all()
            
            if not music_list:
                raise ValueError(f"音乐分组 {music_group} 中没有可用音乐")
            
            if job_config['music_selection'] == 'specific':
                music_id = job_config.get('music_id')
                if not music_id:
                    raise ValueError("指定音乐模式下必须提供音乐ID")
                
                music = next((m for m in music_list if m.id == music_id), None)
                if not music:
                    raise ValueError(f"指定的音乐不存在: {music_id}")
                
                return music
            else:
                return random.choice(music_list)
        finally:
            db.close()
    
    async def _compose_video(
        self, task, materials: List[VideoMaterial], 
        background_music: BackgroundMusic, audio_analysis: AudioAnalysis,
        cover_path: str, subtitle_path: str, job_config: Dict[str, Any]
    ) -> str:
        """合成最终视频"""
        db = self.session_maker()
        try:
            # 使用新的命名规则
            filename = self._generate_standard_filename(task, "mp4")
            relative_video_path = Path("uploads") / "videos" / filename
            
            video_settings = job_config.get('video_settings', {
                'resolution': '1080x1920', 'fps': 30, 'format': 'mp4'
            })
            
            audio_settings = job_config.get('audio_settings')
            subtitle_config = job_config.get('subtitle_config')
            
            success = await VideoCompositionService.compose_video(
                task=task,
                materials=materials,
                background_music=background_music,
                audio_duration=audio_analysis.total_duration,
                cover_image_path=cover_path,
                first_sentence_duration=audio_analysis.first_sentence_duration,
                subtitle_file_path=subtitle_path,
                output_path=str(relative_video_path),
                video_settings=video_settings,
                audio_settings=audio_settings,
                subtitle_settings=subtitle_config
            )
            
            if not success or not (BACKEND_DIR / relative_video_path).exists():
                raise ValueError("视频合成失败")
            
            # 执行文件导出（文案、语音、视频到统一目录）
            audio_path = BACKEND_DIR / task.audio_file_path
            video_path = BACKEND_DIR / relative_video_path
            await self._export_generated_files(task, task.generated_story, str(audio_path), str(video_path))
            
            # 清理中间文件，节省磁盘空间
            # 传入具体的视频路径，因为此时task.final_video_path还没有设置
            await self._cleanup_intermediate_files(task, video_file_path=str(video_path))
            
            return str(relative_video_path)
        finally:
            db.close()
    
    async def _generate_simple_cover(
        self, template, account, title: str, output_path: str
    ) -> bool:
        """生成简单的文本封面，确保在任何情况下都能生成一个文件"""
        try:
            from PIL import Image, ImageDraw, ImageFont  # type: ignore
            
            width, height = 1080, 1920
            image = Image.new('RGB', (width, height), (26, 26, 26))  # type: ignore
            draw = ImageDraw.Draw(image)
            
            try:
                # 尝试加载一个常见的、可能存在的字体
                font_path = "arial.ttf"
                font_title = ImageFont.truetype(font_path, 60)
                font_account = ImageFont.truetype(font_path, 40)
            except IOError:
                logger.warning(f"字体文件 'arial.ttf' 未找到。将只生成背景图。")
                font_title = None
                font_account = None

            if font_account:
                account_text = f"@{account.name}"
                try:
                    text_bbox = draw.textbbox((0, 0), account_text, font=font_account)
                    text_width = text_bbox[2] - text_bbox[0]
                    draw.text(
                        ((width - text_width) // 2, 150), account_text, fill='#ffffff', font=font_account
                    )
                except Exception as e:
                    logger.warning(f"绘制账号文本失败: {e}")

            if font_title:
                try:
                    title_lines = self._wrap_text(title, font_title, width - 100)
                    y_offset = 400
                    for line in title_lines:
                        text_bbox = draw.textbbox((0, 0), line, font=font_title)
                        text_width = text_bbox[2] - text_bbox[0]
                        draw.text(
                            ((width - text_width) // 2, y_offset), line, fill='#ffffff', font=font_title
                        )
                        y_offset += 80
                except Exception as e:
                    logger.warning(f"绘制标题文本失败: {e}")
            
            image.save(output_path, 'PNG')
            logger.info(f"成功生成封面图片: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"封面生成过程中发生严重错误: {str(e)}")
            return False
    
    def _wrap_text(self, text: str, font, max_width: int) -> List[str]:
        """文本自动换行"""
        words = text.split()
        lines = []
        current_line = []
        
        for word in words:
            test_line = ' '.join(current_line + [word])
            bbox = font.getbbox(test_line)
            if bbox[2] <= max_width or not current_line:
                current_line.append(word)
            else:
                lines.append(' '.join(current_line))
                current_line = [word]
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return lines

    def _generate_standard_filename(self, task, extension: str) -> str:
        """
        生成标准的文件名
        格式：账号名_task执行时间(yyyyMMddHHmmss)_摘录到的第一句文案.扩展名
        """
        db = self.session_maker()
        try:
            # 获取账号信息
            account = db.query(Account).filter(Account.id == task.account_id).first()
            if not account:
                raise ValueError(f"账号不存在: {task.account_id}")

            # 获取task执行时间，如果没有则使用当前时间
            if hasattr(task, 'created_at') and task.created_at:
                execution_time = task.created_at
            else:
                execution_time = datetime.now()

            # 格式化时间为yyyyMMddHHmmss
            time_str = execution_time.strftime('%Y%m%d%H%M%S')

            # 获取第一句文案，如果没有则使用默认值
            first_sentence = getattr(task, 'first_sentence', '默认文案')
            if not first_sentence or first_sentence.strip() == '':
                first_sentence = '默认文案'

            # 清理文件名中的特殊字符，保留中文、英文、数字、下划线和连字符
            safe_account_name = re.sub(r'[^\w\u4e00-\u9fff\-]', '_', account.name)
            # safe_first_sentence = re.sub(r'[^\w\u4e00-\u9fff\-]', '_', first_sentence.strip())
            safe_first_sentence = re.sub(r'[^\w\u4e00-\u9fff\-\s]', '_', first_sentence.strip())
            
            # 限制第一句文案的长度，避免文件名过长
            if len(safe_first_sentence) > 100:
                safe_first_sentence = safe_first_sentence[:100]

            # 生成文件名：账号名_执行时间_第一句文案.扩展名
            filename = f"{safe_account_name}_{time_str}_{safe_first_sentence}.{extension}"

            logger.info(f"生成文件名: {filename} (账号: {account.name}, 时间: {time_str}, 文案: {first_sentence[:20]}...)")
            return filename

        finally:
            db.close()
    
    def _get_account_sequence_in_batch(self, job_id: str, account_id: str, current_task_id: str) -> int:
        """
        获取该账号在当前批次中的序号
        """
        db = self.session_maker()
        try:
            # 查找同一作业中该账号的所有任务，按创建时间排序
            tasks = db.query(VideoGenerationTask).filter(
                VideoGenerationTask.job_id == job_id,
                VideoGenerationTask.account_id == account_id
            ).order_by(VideoGenerationTask.created_at).all()
            
            # 找到当前任务在列表中的位置，序号从1开始
            for i, task in enumerate(tasks, 1):
                if task.id == current_task_id:
                    return i
            
            # 如果没找到，返回1作为默认值
            logger.warning(f"未找到任务 {current_task_id} 在作业 {job_id} 账号 {account_id} 中的序号，使用默认值1")
            return 1
            
        finally:
            db.close()
    
    async def _export_generated_files(self, task, story: str, audio_path: str, video_path: str):
        """
        导出生成的文件（文案、语音、视频、封面）到统一目录，使用统一命名
        """
        try:
            # 创建导出目录
            export_dir = UPLOADS_DIR / "exports"
            export_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成基础文件名（不含扩展名）
            base_filename = self._generate_standard_filename(task, "").rstrip(".")
            
            # 1. 导出文案文件
            story_filename = f"{base_filename}.txt"
            story_path = export_dir / story_filename
            with open(story_path, 'w', encoding='utf-8') as f:
                f.write(story)
            logger.info(f"已导出文案文件: {story_path}")
            
            # 2. 复制语音文件到导出目录
            audio_filename = f"{base_filename}.mp3"
            export_audio_path = export_dir / audio_filename
            import shutil
            shutil.copy2(audio_path, export_audio_path)
            logger.info(f"已导出语音文件: {export_audio_path}")
            
            # 3. 复制封面文件到导出目录
            cover_filename = None
            if hasattr(task, 'cover_image_path') and task.cover_image_path:
                try:
                    # 处理封面文件路径（绝对路径或相对路径）
                    if os.path.isabs(task.cover_image_path):
                        cover_source_path = Path(task.cover_image_path)
                    else:
                        cover_source_path = BACKEND_DIR / task.cover_image_path
                    
                    if cover_source_path.exists():
                        cover_filename = f"{base_filename}.png"
                        export_cover_path = export_dir / cover_filename
                        shutil.copy2(cover_source_path, export_cover_path)
                        logger.info(f"已导出封面文件: {export_cover_path}")
                    else:
                        logger.warning(f"封面文件不存在，跳过导出: {cover_source_path}")
                except Exception as e:
                    logger.error(f"导出封面文件失败: {e}")
            
            # 4. 复制视频文件到导出目录
            video_filename = f"{base_filename}.mp4"
            export_video_path = export_dir / video_filename
            shutil.copy2(video_path, export_video_path)
            logger.info(f"已导出视频文件: {export_video_path}")
            
            logger.info(f"✅ 文件导出完成，导出目录: {export_dir}")
            logger.info(f"   - 文案: {story_filename}")
            logger.info(f"   - 语音: {audio_filename}")
            logger.info(f"   - 封面: {cover_filename if cover_filename else '无'}")
            logger.info(f"   - 视频: {video_filename}")
            
        except Exception as e:
            logger.error(f"文件导出失败: {e}")
            # 不抛出异常，避免影响主流程

    async def _cleanup_intermediate_files(self, task, video_file_path: Optional[str] = None):
        """
        清理中间生成的文件，只保留导出目录中的最终文件
        """
        try:
            import os
            
            logger.info(f"开始清理任务 {task.id} 的中间文件...")
            
            files_to_clean = []
            
            # 1. 清理音频文件（uploads/audio目录中的原始文件）
            if hasattr(task, 'audio_file_path') and task.audio_file_path:
                # 处理绝对路径和相对路径
                if os.path.isabs(task.audio_file_path):
                    audio_file = Path(task.audio_file_path)
                else:
                    audio_file = BACKEND_DIR / task.audio_file_path
                
                if audio_file.exists():
                    files_to_clean.append(audio_file)
                    logger.debug(f"将清理音频文件: {audio_file}")
            
            # 2. 清理封面文件（uploads/covers目录中的原始文件）
            if hasattr(task, 'cover_image_path') and task.cover_image_path:
                # 处理绝对路径和相对路径
                if os.path.isabs(task.cover_image_path):
                    cover_file = Path(task.cover_image_path)
                else:
                    cover_file = BACKEND_DIR / task.cover_image_path
                
                if cover_file.exists():
                    files_to_clean.append(cover_file)
                    logger.debug(f"将清理封面文件: {cover_file}")
            
            # 3. 清理字幕文件（uploads/subtitles目录中的原始文件）
            if hasattr(task, 'subtitle_file_path') and task.subtitle_file_path:
                # 处理绝对路径和相对路径
                if os.path.isabs(task.subtitle_file_path):
                    subtitle_file = Path(task.subtitle_file_path)
                else:
                    subtitle_file = BACKEND_DIR / task.subtitle_file_path
                
                if subtitle_file.exists():
                    files_to_clean.append(subtitle_file)
                    logger.debug(f"将清理字幕文件: {subtitle_file}")
                
                # 同时清理可能的ASS字幕文件
                ass_file = subtitle_file.with_suffix('.ass')
                if ass_file.exists():
                    files_to_clean.append(ass_file)
                    logger.debug(f"将清理ASS字幕文件: {ass_file}")
            
            # 4. 清理视频文件（uploads/videos目录中的原始文件）
            # 优先使用传入的video_file_path参数，如果没有则使用task.final_video_path
            video_path_to_clean = video_file_path
            if not video_path_to_clean and hasattr(task, 'final_video_path') and task.final_video_path:
                video_path_to_clean = task.final_video_path
            
            if video_path_to_clean:
                # 处理绝对路径和相对路径
                if os.path.isabs(video_path_to_clean):
                    video_file = Path(video_path_to_clean)
                else:
                    video_file = BACKEND_DIR / video_path_to_clean
                
                if video_file.exists():
                    files_to_clean.append(video_file)
                    logger.debug(f"将清理视频文件: {video_file}")
                else:
                    logger.debug(f"视频文件不存在，跳过清理: {video_file}")
            else:
                logger.debug("没有找到需要清理的视频文件路径")
            
            # 5. 清理临时目录中与该任务相关的文件
            temp_dir = UPLOADS_DIR / "temp"
            if temp_dir.exists():
                for temp_file in temp_dir.glob(f"*{task.id}*"):
                    if temp_file.is_file():
                        files_to_clean.append(temp_file)
                        logger.debug(f"将清理临时文件: {temp_file}")
            
            # 执行文件删除
            deleted_count = 0
            total_size = 0
            
            logger.info(f"准备清理 {len(files_to_clean)} 个中间文件")
            
            for file_path in files_to_clean:
                try:
                    if file_path.exists():
                        file_size = file_path.stat().st_size
                        total_size += file_size
                        os.remove(file_path)
                        deleted_count += 1
                        logger.debug(f"已删除中间文件: {file_path} ({file_size} bytes)")
                    else:
                        logger.debug(f"文件不存在，跳过: {file_path}")
                except Exception as e:
                    logger.warning(f"删除文件失败 {file_path}: {e}")
            
            if deleted_count > 0:
                logger.info(f"✅ 中间文件清理完成: 删除了 {deleted_count} 个文件，释放空间 {total_size / 1024 / 1024:.2f} MB")
            else:
                logger.info("没有需要清理的中间文件")
                
        except Exception as e:
            logger.error(f"清理中间文件时发生错误: {e}")
            # 不抛出异常，避免影响主流程

# 将辅助方法整合到主服务类中
def integrate_helpers(VideoGenerationService):
    """将辅助方法整合到主服务类中"""
    from sqlalchemy.orm import sessionmaker
    
    # 创建一个假的session_maker用于类型检查
    # 实际使用时会被正确的session_maker替换
    dummy_session_maker = sessionmaker()  # type: ignore
    helpers = VideoGenerationServiceHelpers(dummy_session_maker)
    
    # 复制所有辅助方法到主服务类
    for method_name in dir(helpers):
        if method_name.startswith('_') and callable(getattr(helpers, method_name)):
            def create_method(name):
                async def method(self, *args, **kwargs):
                    helper = VideoGenerationServiceHelpers(self)
                    return await getattr(helper, name)(*args, **kwargs)
                return method
            
            setattr(VideoGenerationService, method_name, create_method(method_name))