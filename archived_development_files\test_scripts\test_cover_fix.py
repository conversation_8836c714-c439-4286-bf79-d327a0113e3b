#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试封面截图修复
"""

import requests
import json
import time

def test_video_generation():
    """测试视频生成API"""
    url = "http://localhost:8000/api/video-generation/generate"
    
    # 简单的测试数据
    test_data = {
        "account_id": 1,
        "story_content": "这是一个测试故事，用来验证封面截图修复是否成功。",
        "voice_config": {
            "voice_id": "zh-CN-XiaoxiaoNeural",
            "speed": 1.0,
            "pitch": 0,
            "volume": 0.8
        },
        "subtitle_config": {
            "enabled": True,
            "mode": "srt",
            "position": "bottom",
            "font_size": 24,
            "font_color": "#FFFFFF"
        },
        "cover_config": {
            "template_id": "faeeface-f5a8-4ca3-a0bd-e38471b0a82f"
        },
        "background_music": {
            "enabled": False
        }
    }
    
    print("🧪 开始测试视频生成...")
    print(f"📝 测试数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ 视频生成任务已提交: {task_id}")
            
            # 监控任务状态
            status_url = f"http://localhost:8000/api/video-generation/status/{task_id}"
            
            print("⏳ 监控任务进度...")
            for i in range(60):  # 最多等待60次，每次5秒
                try:
                    status_response = requests.get(status_url, timeout=10)
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        status = status_data.get('status')
                        progress = status_data.get('progress', 0)
                        
                        print(f"📊 [{i*5:3d}s] 状态: {status}, 进度: {progress}%")
                        
                        if status == 'completed':
                            print("✅ 视频生成成功！")
                            return True
                        elif status == 'failed':
                            error = status_data.get('error', '未知错误')
                            print(f"❌ 视频生成失败: {error}")
                            return False
                    
                    time.sleep(5)
                except Exception as e:
                    print(f"⚠️ 状态查询出错: {e}")
                    time.sleep(5)
            
            print("⏰ 任务超时，请手动查看日志")
            return False
            
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 封面截图修复验证测试")
    print("=" * 60)
    
    success = test_video_generation()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 修复验证测试通过！")
        print("💡 封面截图相关错误已成功修复")
    else:
        print("❌ 修复验证测试失败")
        print("💡 可能还有其他问题需要进一步排查")
    print("=" * 60)

if __name__ == "__main__":
    main()
