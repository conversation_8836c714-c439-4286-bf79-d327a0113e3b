"""
视频生成任务相关数据模型
"""

from sqlalchemy import Column, String, Integer, Text, Boolean, JSON, DateTime, Float, ForeignKey
from sqlalchemy.orm import relationship
from . import BaseModel
from enum import Enum


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"         # 等待中
    RUNNING = "running"         # 运行中
    PAUSED = "paused"          # 已暂停
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 失败
    CANCELLED = "cancelled"     # 已取消


class VideoGenerationJob(BaseModel):
    """视频生成作业（批量任务的容器）"""
    __tablename__ = "video_generation_jobs"
    
    name = Column(String(255), nullable=False, comment="作业名称")
    description = Column(Text, comment="作业描述")
    
    # 配置参数
    config = Column(JSON, comment="作业配置JSON")
    # {
    #   "video_material_group": "group_id",
    #   "material_selection": "random|manual",  
    #   "prompt_group": "group_id",
    #   "prompt_id": "prompt_id",
    #   "voice_settings": {
    #     "voice": "voice_name",
    #     "speed": 1.0
    #   },
    #   "background_music_group": "group_id", 
    #   "music_selection": "random|specific",
    #   "music_id": "music_id",  # if specific
    #   "cover_template_id": "template_id",
    #   "subtitle_settings": {
    #     "font": "Arial",
    #     "size": 24,
    #     "color": "#FFFFFF"
    #   },
    #   "video_settings": {
    #     "resolution": "1080x1920",  # 竖屏
    #     "fps": 30,
    #     "format": "mp4"
    #   }
    # }
    
    # 账号和数量配置
    account_configs = Column(JSON, comment="账号配置")
    # [
    #   {"account_id": "id1", "video_count": 5},
    #   {"account_id": "id2", "video_count": 3}
    # ]
    
    # 作业状态
    status = Column(String(20), default=TaskStatus.PENDING, comment="作业状态")
    total_tasks = Column(Integer, default=0, comment="总任务数")
    completed_tasks = Column(Integer, default=0, comment="已完成任务数") 
    failed_tasks = Column(Integer, default=0, comment="失败任务数")
    
    # 时间统计
    started_at = Column(DateTime, comment="开始时间")
    completed_at = Column(DateTime, comment="完成时间")
    estimated_duration = Column(Integer, comment="预计时长(秒)")
    
    # 错误信息
    error_message = Column(Text, comment="错误信息")


class VideoGenerationTask(BaseModel):
    """单个视频生成任务"""
    __tablename__ = "video_generation_tasks"
    
    # 关联的作业
    job_id = Column(String(36), ForeignKey("video_generation_jobs.id"), nullable=False)
    job = relationship("VideoGenerationJob", backref="tasks")
    
    # 任务基本信息
    task_name = Column(String(255), nullable=False, comment="任务名称")
    account_id = Column(String(36), nullable=False, comment="关联账号ID")
    
    # 任务状态和进度
    status = Column(String(20), default=TaskStatus.PENDING, comment="任务状态")
    progress = Column(Float, default=0.0, comment="进度百分比 0-100")
    current_step = Column(String(100), comment="当前步骤")
    
    # 生成的内容
    generated_story = Column(Text, comment="生成的故事文案")
    first_sentence = Column(String(500), comment="第一句话")
    audio_file_path = Column(String(500), comment="音频文件路径")
    subtitle_file_path = Column(String(500), comment="字幕文件路径")
    cover_image_path = Column(String(500), comment="封面图片路径")
    final_video_path = Column(String(500), comment="最终视频路径")
    
    # 使用的资源
    used_materials = Column(JSON, comment="使用的视频素材ID列表")
    used_music_id = Column(String(36), comment="使用的背景音乐ID")
    
    # 时间统计
    started_at = Column(DateTime, comment="开始时间")
    completed_at = Column(DateTime, comment="完成时间")
    duration = Column(Integer, comment="任务耗时(秒)")
    
    # 音频分析结果
    audio_analysis = Column(JSON, comment="音频分析结果")
    # {
    #   "total_duration": 45.2,
    #   "first_sentence_duration": 3.5,
    #   "word_timestamps": [
    #     {"word": "Hello", "start": 0.0, "end": 0.5},
    #     {"word": "world", "start": 0.6, "end": 1.1}
    #   ]
    # }
    
    # 错误信息
    error_message = Column(Text, comment="错误信息")
    retry_count = Column(Integer, default=0, comment="重试次数")


class TaskLog(BaseModel):
    """任务执行日志"""
    __tablename__ = "task_logs"
    
    task_id = Column(String(36), ForeignKey("video_generation_tasks.id"), nullable=False)
    task = relationship("VideoGenerationTask", backref="logs")
    
    level = Column(String(10), nullable=False, comment="日志级别: INFO, WARN, ERROR")
    step = Column(String(100), comment="执行步骤")
    message = Column(Text, nullable=False, comment="日志消息")
    details = Column(JSON, comment="详细信息")
    timestamp = Column(DateTime, nullable=False, comment="时间戳")
