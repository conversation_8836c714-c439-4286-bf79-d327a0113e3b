"""
快速测试创建模板API
"""

import requests
import json

def quick_test():
    url = "http://localhost:8000/api/cover-templates"
    
    data = {
        "name": "test0629",
        "category": "经典",
        "description": "1",
        "elements": [],
        "background": {
            "type": "gradient",
            "value": {
                "direction": "to bottom right",
                "colors": ["#667eea", "#764ba2"]
            }
        }
    }
    
    print(f"🔗 请求URL: {url}")
    print(f"📦 请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        print(f"📄 响应内容: {response.text}")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    quick_test()
