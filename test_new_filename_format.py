#!/usr/bin/env python3
"""
测试新的文件命名格式
格式：账号名_task执行时间(yyyyMMddHHmmss)_摘录到的第一句文案.扩展名
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent / "backend"
sys.path.insert(0, str(project_root))

from src.services.video_generation_helpers import VideoGenerationServiceHelpers
from src.core.database import get_session_maker
from src.models.accounts import Account
from src.models.video_generation import VideoGenerationJob, VideoGenerationTask

def test_filename_generation():
    """测试新的文件命名格式"""
    
    print("🚀 测试新的文件命名格式...")
    
    # 获取数据库会话
    session_maker = get_session_maker()
    db = session_maker()
    
    try:
        # 1. 获取一个测试账号
        account = db.query(Account).first()
        if not account:
            print("❌ 错误: 系统中没有可用的账号")
            return False
        
        print(f"✅ 使用测试账号: {account.name}")
        
        # 2. 创建一个模拟的任务对象
        class MockTask:
            def __init__(self):
                self.account_id = account.id
                self.created_at = datetime.now()
                self.first_sentence = "这是一个测试的第一句话，用来验证文件命名功能。"
        
        # 3. 创建辅助服务实例
        helper = VideoGenerationServiceHelpers(session_maker)
        
        # 4. 测试不同扩展名的文件命名
        mock_task = MockTask()
        
        print("\n📋 测试文件命名规则:")
        print(f"   账号名: {account.name}")
        print(f"   执行时间: {mock_task.created_at.strftime('%Y%m%d%H%M%S')}")
        print(f"   第一句话: {mock_task.first_sentence}")
        print()
        
        for ext in ['mp3', 'mp4', 'txt', 'srt', 'png']:
            filename = helper._generate_standard_filename(mock_task, ext)
            print(f"   {ext.upper()}文件: {filename}")
        
        # 5. 测试特殊字符处理
        print("\n🔧 测试特殊字符处理:")
        
        class MockTaskSpecial:
            def __init__(self):
                self.account_id = account.id
                self.created_at = datetime.now()
                self.first_sentence = "这是一个包含特殊字符的测试！@#$%^&*()文案？"
        
        mock_task_special = MockTaskSpecial()
        filename_special = helper._generate_standard_filename(mock_task_special, 'mp4')
        print(f"   原始文案: {mock_task_special.first_sentence}")
        print(f"   处理后文件名: {filename_special}")
        
        # 6. 测试长文案处理
        print("\n📏 测试长文案处理:")
        
        class MockTaskLong:
            def __init__(self):
                self.account_id = account.id
                self.created_at = datetime.now()
                self.first_sentence = "这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的第一句话，用来测试文件名长度限制功能。"
        
        mock_task_long = MockTaskLong()
        filename_long = helper._generate_standard_filename(mock_task_long, 'mp4')
        print(f"   原始文案长度: {len(mock_task_long.first_sentence)} 字符")
        print(f"   处理后文件名: {filename_long}")
        print(f"   文件名长度: {len(filename_long)} 字符")
        
        # 7. 测试空文案处理
        print("\n🔍 测试空文案处理:")
        
        class MockTaskEmpty:
            def __init__(self):
                self.account_id = account.id
                self.created_at = datetime.now()
                self.first_sentence = ""
        
        mock_task_empty = MockTaskEmpty()
        filename_empty = helper._generate_standard_filename(mock_task_empty, 'mp4')
        print(f"   空文案处理结果: {filename_empty}")
        
        print("\n✅ 文件命名格式测试完成！")
        print("\n📝 新格式说明:")
        print("   格式: 账号名_执行时间(yyyyMMddHHmmss)_第一句文案.扩展名")
        print("   - 特殊字符会被替换为下划线")
        print("   - 第一句文案长度限制为50字符")
        print("   - 空文案会使用'默认文案'")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    success = test_filename_generation()
    sys.exit(0 if success else 1)
