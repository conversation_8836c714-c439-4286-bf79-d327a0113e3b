/**
 * 全局状态类型定义
 */

// ================================
// 通知相关类型
// ================================

export interface NotificationMessage {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number // 显示时长(ms)，不传则不自动消失
  timestamp: number
}

export interface NotificationState {
  notifications: NotificationMessage[]
  addNotification: (notification: Omit<NotificationMessage, 'id' | 'timestamp'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
}

// ================================
// 设置相关类型
// ================================

export interface TTSConfig {
  provider: 'openai' | 'azure' | 'elevenlabs' | 'local' | 'coze'
  apiKey?: string
  endpoint?: string
  voice: string
  model?: string
  speed: number
  pitch: number
  volume: number
}

export interface LLMConfig {
  provider: 'openai' | 'claude' | 'local' | 'azure' | 'yunwu'
  apiKey?: string
  endpoint?: string
  model: string
  temperature: number
  maxTokens: number
  systemPrompt: string
}

export interface GeneralSettings {
  theme: 'light' | 'dark' | 'system'
  language: 'zh-CN' | 'en-US'
  autoSave: boolean
  showTips: boolean
  outputDirectory: string
}

export interface SettingsState {
  tts: TTSConfig
  llm: LLMConfig
  general: GeneralSettings
  
  // Actions
  updateTTSConfig: (config: Partial<TTSConfig>) => void
  updateLLMConfig: (config: Partial<LLMConfig>) => void
  updateGeneralSettings: (settings: Partial<GeneralSettings>) => void
  resetToDefault: () => void
  loadFromBackend: () => Promise<void>
  saveToBackend: () => Promise<boolean>
  // 兼容旧接口
  loadFromStorage: () => Promise<void>
  saveToStorage: () => Promise<boolean>
}

// ================================
// 资源管理相关类型
// ================================

export interface BackgroundMusic {
  id: string
  name: string
  filePath: string
  duration: number
  category: string
  tags: string[]
  isBuiltIn: boolean
}

export interface VideoMaterial {
  id: string
  name: string
  filePath: string
  duration: number
  category: string
  resolution: string
  tags: string[]
  isBuiltIn: boolean
}

export interface Prompt {
  id: string
  name: string
  content: string
  category: string
  variables: string[]
  isBuiltIn: boolean
}

export interface CoverTemplate {
  id: string
  name: string
  previewPath: string
  templatePath: string
  variables: string[]
  isBuiltIn: boolean
}

// 资源管理中使用的简化账户类型
export interface ResourceAccount {
  id: string
  name: string
  platform: string
  isActive: boolean
}

export interface ResourceState {
  backgroundMusic: BackgroundMusic[]
  videoMaterials: VideoMaterial[]
  prompts: Prompt[]
  accounts: ResourceAccount[]
  coverTemplates: CoverTemplate[]
  
  // 选中状态
  selectedBackgroundMusic: string | null
  selectedVideoMaterials: string[]
  selectedPrompt: string | null
  selectedAccount: string | null
  selectedCoverTemplate: string | null
  
  // 背景音乐操作
  addBackgroundMusic: (music: Omit<BackgroundMusic, 'id'>) => string
  updateBackgroundMusic: (id: string, updates: Partial<BackgroundMusic>) => void
  removeBackgroundMusic: (id: string) => void
  selectBackgroundMusic: (id: string | null) => void
  
  // 视频素材操作
  addVideoMaterial: (material: Omit<VideoMaterial, 'id'>) => string
  updateVideoMaterial: (id: string, updates: Partial<VideoMaterial>) => void
  removeVideoMaterial: (id: string) => void
  selectVideoMaterials: (ids: string[]) => void
  
  // 提示词操作
  addPrompt: (prompt: Omit<Prompt, 'id'>) => string
  updatePrompt: (id: string, updates: Partial<Prompt>) => void
  removePrompt: (id: string) => void
  selectPrompt: (id: string | null) => void
  
  // 账户操作
  addAccount: (account: Omit<ResourceAccount, 'id'>) => string
  updateAccount: (id: string, updates: Partial<ResourceAccount>) => void
  removeAccount: (id: string) => void
  selectAccount: (id: string | null) => void
  
  // 封面模板操作
  addCoverTemplate: (template: Omit<CoverTemplate, 'id'>) => string
  updateCoverTemplate: (id: string, updates: Partial<CoverTemplate>) => void
  removeCoverTemplate: (id: string) => void
  selectCoverTemplate: (id: string | null) => void
  
  // 重置资源
  resetResources: () => void
}

// ================================
// 视频生成相关类型
// ================================

export interface GenerationConfig {
  selectedPrompt: string
  selectedAccount: string
  selectedBackgroundMusic: string
  selectedVideoMaterials: string[]
  selectedCoverTemplate: string
  
  // Video settings
  videoDuration: number
  videoResolution: '1080p' | '720p' | '4K'
  frameRate: 30 | 60
  
  // Audio settings
  backgroundMusicVolume: number
  voiceVolume: number
  
  // Subtitle settings
  showSubtitles: boolean
  subtitleStyle: {
    fontFamily: string
    fontSize: number
    color: string
    backgroundColor: string
    position: 'top' | 'center' | 'bottom'
  }
}

export interface GenerationTask {
  id: string
  config: GenerationConfig
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  currentStep: string
  startTime?: Date
  endTime?: Date
  outputPath?: string
  error?: string
}

export interface GenerationState {
  config: GenerationConfig
  tasks: GenerationTask[]
  currentTask?: string
  
  // Actions
  updateConfig: (config: Partial<GenerationConfig>) => void
  createTask: (config: GenerationConfig) => string
  updateTaskProgress: (taskId: string, progress: number, step: string) => void
  updateTaskStatus: (taskId: string, status: GenerationTask['status'], error?: string) => void
  removeTask: (taskId: string) => void
  clearCompletedTasks: () => void
  
  // Internal method
  startGeneration: (taskId: string) => Promise<void>
}

// ================================
// 全局状态接口
// ================================

export interface GlobalState {
  settings: SettingsState
  resources: ResourceState
  generation: GenerationState
}

// ================================
// API相关类型
// ================================

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface ApiError {
  message: string
  status: number
  details?: any
}

// ================================
// 账号管理相关类型
// ================================

export enum PlatformType {
  REDDIT = 'reddit',
  YOUTUBE = 'youtube',
  TIKTOK = 'tiktok',
  BILIBILI = 'bilibili',
  INSTAGRAM = 'instagram',
  TWITTER = 'twitter'
}

export enum AccountStatus {
  UNUSED = 'unused',
  USED = 'used',
  DISABLED = 'disabled'
}

export interface Account {
  id: number
  name: string
  description?: string
  platform: PlatformType
  status: AccountStatus
  avatar_url?: string
  
  // 个性化设置
  brand_color: string
  font_style: string
  content_style: string
  platform_settings: Record<string, any>
  
  // 统计信息
  usage_count: number
  created_at: string
  updated_at: string
  last_used_at?: string
}

export interface AccountCreate {
  name: string
  description?: string
  platform: PlatformType
  avatar_url?: string
  brand_color?: string
  font_style?: string
  content_style?: string
  platform_settings?: Record<string, any>
}

export interface AccountUpdate {
  name?: string
  description?: string
  platform?: PlatformType
  avatar_url?: string
  status?: AccountStatus
  brand_color?: string
  font_style?: string
  content_style?: string
  platform_settings?: Record<string, any>
}

export interface AccountList {
  accounts: Account[]
  total: number
  page: number
  page_size: number
}

export interface AccountStats {
  total_accounts: number
  by_platform: Record<PlatformType, number>
  by_status: Record<AccountStatus, number>
  most_used?: Account
  recently_created: Account[]
}

export interface AccountState {
  accounts: Account[]
  selectedAccounts: number[]
  currentAccount: Account | null
  stats: AccountStats | null
  loading: boolean
  
  // Actions
  loadAccounts: (params?: {
    page?: number
    page_size?: number
    platform?: PlatformType
    status?: AccountStatus
    search?: string
  }) => Promise<void>
  
  loadAccountStats: () => Promise<void>
  createAccount: (account: AccountCreate) => Promise<Account>
  updateAccount: (id: number, updates: AccountUpdate) => Promise<Account>
  deleteAccount: (id: number) => Promise<void>
  bulkDeleteAccounts: (ids: number[]) => Promise<void>
  bulkUpdateStatus: (ids: number[], status: AccountStatus) => Promise<void>
  uploadAvatar: (id: number, file: File) => Promise<string>
  useAccount: (id: number) => Promise<Account>
  
  setSelectedAccounts: (ids: number[]) => void
  setCurrentAccount: (account: Account | null) => void
  clearState: () => void
}

// ================================
// 资源管理相关类型 (更新后的Account类型已移除，使用上面的新定义)
// ================================
