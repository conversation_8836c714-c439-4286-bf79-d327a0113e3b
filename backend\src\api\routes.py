"""
Main API router that includes all route modules
"""

from fastapi import APIRouter

# Import route modules
# from .auth import router as auth_router
from .settings import router as settings_router
from .music import router as music_router
from .video import router as video_router
from .video_categories import router as video_categories_router
from .prompt import router as prompt_router
from .accounts import router as accounts_router
from .cover_template import router as cover_template_router
from .llm import router as llm_router
from .video_generation import router as video_generation_router
# from .upload import router as upload_router  # 已移除，功能整合到video.py
# from .generation import router as generation_router

api_router = APIRouter()

# Health check for API
@api_router.get("/health")
async def api_health():
    """API health check endpoint"""
    return {
        "status": "healthy",
        "service": "reddit-story-generator-api",
        "version": "0.1.0"
    }

# Include route modules
# api_router.include_router(auth_router, prefix="/auth", tags=["authentication"])
api_router.include_router(settings_router, prefix="/settings", tags=["settings"])

# File upload routes - 现在由video.py处理
# api_router.include_router(upload_router)  # 已移除

# Resource management routes (each router has its own prefix defined)
api_router.include_router(music_router)  # prefix="/background-music" 
api_router.include_router(video_router)  # prefix="/video-materials" 
api_router.include_router(video_categories_router)  # prefix="/video-categories"
api_router.include_router(prompt_router)  # prefix="/prompts" 
api_router.include_router(accounts_router)  # prefix="/accounts" 
api_router.include_router(cover_template_router)  # prefix="/cover-templates"
api_router.include_router(llm_router)  # prefix="/llm"
api_router.include_router(video_generation_router)  # prefix="/video-generator"

# api_router.include_router(generation_router, prefix="/generation", tags=["generation"])
