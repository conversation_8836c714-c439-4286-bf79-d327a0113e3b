"""
TTS服务
"""

import asyncio
import aiohttp
import json
import os
import subprocess
from pathlib import Path
from typing import Optional
from sqlalchemy.orm import Session, sessionmaker
from loguru import logger
import traceback

from ..services.settings_service import get_current_tts_config
from ..schemas.settings import TTSConfig


class TTSService:
    """TTS服务"""
    
    def __init__(self, session_maker: sessionmaker):
        self.session_maker = session_maker
    
    async def generate_speech(
        self, text: str, voice: str, speed: float = 1.0, output_path: Optional[str] = None
    ) -> bool:
        """
        生成语音，直接处理整个文本块
        """
        db = self.session_maker()
        try:
            if not text or not text.strip():
                raise ValueError("没有有效的文本内容用于TTS")

            tts_config = get_current_tts_config(db)
            if not tts_config:
                raise ValueError("TTS配置未找到")

            if not tts_config.get('apiKey'):
                raise ValueError("Coze TTS Token (API Key) 未配置")
            
            if tts_config.get('provider') == "coze" and not tts_config.get('model'):
                raise ValueError("Coze TTS Workflow ID (Model) 未配置")

            # 直接调用TTS API处理整个文本
            success = await self._call_coze_tts_api(
                tts_config, text, voice, speed, output_path
            )
            
            if not success:
                raise RuntimeError("使用Coze TTS API生成语音失败")

            return True

        except Exception as e:
            logger.error(f"语音生成失败: {e}")
            return False
        finally:
            db.close()
    
    async def _call_coze_tts_api(
        self, config: dict, text: str, voice: str, speed: float, output_path: Optional[str]
    ) -> bool:
        """调用Coze TTS工作流并下载音频文件"""
        import httpx
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {config.get('apiKey')}"
            }
            
            coze_params = {
                "speaker_id": voice,
                "speed_ratio": speed,
                "text": text
            }
            
            data = {
                "workflow_id": config.get('model'),
                "parameters": coze_params
            }
            
            logger.debug(f"向Coze TTS API发送请求, data: {json.dumps(data, indent=2)}")

            # 用aiohttp重写Coze TTS调用和音频下载逻辑
            url = "https://api.coze.cn/v1/workflow/run"
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    logger.debug(f"Coze TTS API 响应状态: {response.status}")
                    response_text = await response.text()
                    logger.debug(f"Coze TTS API 响应内容: {response_text}")

                    if response.status != 200:
                        raise RuntimeError(f"Coze Workflow API调用失败: {response.status} - {response_text}")

                    result = json.loads(response_text)
                    if result.get("code") != 0:
                        raise RuntimeError(f"Coze API错误: {result.get('msg', '未知错误')}")

                    result_data = result.get("data", "{}")
                    if isinstance(result_data, str):
                        result_data = json.loads(result_data)

                    audio_url = result_data.get("data", {}).get("url")
                    if not audio_url:
                        raise RuntimeError("Coze API未返回有效的音频URL")

                    logger.info(f"获取到Coze TTS音频下载链接: {audio_url}")
                # 重新用aiohttp下载音频
                async with session.get(audio_url, timeout=60.0) as audio_response:
                    if audio_response.status != 200:
                        raise RuntimeError(f"从Coze下载音频文件失败: {audio_response.status}")

                    audio_data = await audio_response.read()
                    if output_path:
                        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                        with open(output_path, 'wb') as f:
                            f.write(audio_data)

                return True

        except Exception as e:
            logger.error(f"Coze TTS调用失败: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    async def _call_azure_tts_api(
        self, config: dict, text: str, voice: str, speed: float, output_path: Optional[str]
    ) -> bool:
        """调用Azure TTS API"""
        try:
            headers = {
                "Content-Type": "application/ssml+xml",
                "X-Microsoft-OutputFormat": "audio-16khz-128kbitrate-mono-mp3",
                "Ocp-Apim-Subscription-Key": config.get('apiKey', '')
            }
            
            # 构建SSML
            speed_value = f"{speed:.1f}"
            ssml = f"""
            <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
                <voice name="{voice}">
                    <prosody rate="{speed_value}">{text}</prosody>
                </voice>
            </speak>
            """
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{config.get('baseUrl', '')}/cognitiveservices/v1", headers=headers, data=ssml) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise RuntimeError(f"Azure TTS API调用失败: {response.status} - {error_text}")
                    
                    # 保存音频文件
                    audio_data = await response.read()
                    
                    if output_path:
                        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                        with open(output_path, 'wb') as f:
                            f.write(audio_data)
                    
                    return True
        except Exception as e:
            print(f"Azure TTS调用失败: {str(e)}")
            return False
    
    async def _call_openai_tts_api(
        self, config: dict, text: str, voice: str, speed: float, output_path: Optional[str]
    ) -> bool:
        """调用OpenAI TTS API"""
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {config.get('apiKey', '')}"
            }
            
            data = {
                "model": "tts-1",
                "input": text,
                "voice": voice,
                "speed": speed
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{config.get('baseUrl', '')}/v1/audio/speech", headers=headers, json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise RuntimeError(f"OpenAI TTS API调用失败: {response.status} - {error_text}")
                    
                    # 保存音频文件
                    audio_data = await response.read()
                    
                    if output_path:
                        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
                        with open(output_path, 'wb') as f:
                            f.write(audio_data)
                    
                    return True
        except Exception as e:
            print(f"OpenAI TTS调用失败: {str(e)}")
            return False
    
    def get_available_voices(self) -> list:
        """获取可用的音色列表"""
        db = self.session_maker()
        try:
            # 获取TTS配置
            tts_config = get_current_tts_config(db)
            if not tts_config:
                return []
            
            if tts_config.get('provider') == "coze":
                return ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
            elif tts_config.get('provider') == "azure":
                return ["en-US-AriaNeural", "en-US-JennyNeural", "en-US-GuyNeural", "en-US-DavisNeural"]
            elif tts_config.get('provider') == "openai":
                return ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
            else:
                return []
        finally:
            db.close()
